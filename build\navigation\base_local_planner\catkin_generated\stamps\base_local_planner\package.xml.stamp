<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
    <name>base_local_planner</name>
    <version>1.17.3</version>
    <description>

        This package provides implementations of the Trajectory Rollout and Dynamic Window approaches to local robot navigation on a plane. Given a plan to follow and a costmap, the controller produces velocity commands to send to a mobile base. This package supports both holonomic and non-holonomic robots, any robot footprint that can be represented as a convex polygon or circle, and exposes its configuration as ROS parameters that can be set in a launch file. This package's ROS wrapper adheres to the BaseLocalPlanner interface specified in the <a href="http://wiki.ros.org/nav_core">nav_core</a> package. 

    </description>
    <author><PERSON><PERSON><PERSON></author>
    <author><PERSON></author>
    <author><EMAIL></author>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <maintainer email="<EMAIL>">David <PERSON>!</maintainer>
    <maintainer email="<EMAIL>">Aaron Hoy</maintainer>
    <license>BSD</license>
    <url>http://wiki.ros.org/base_local_planner</url>

    <buildtool_depend version_gte="0.5.68">catkin</buildtool_depend>
    <buildtool_depend condition="$ROS_PYTHON_VERSION == 2">python-setuptools</buildtool_depend>
    <buildtool_depend condition="$ROS_PYTHON_VERSION == 3">python3-setuptools</buildtool_depend>

    <build_depend>cmake_modules</build_depend>
    <build_depend>message_generation</build_depend>
    <build_depend>tf2_geometry_msgs</build_depend>

    <depend>angles</depend>
    <depend>costmap_2d</depend>
    <depend>dynamic_reconfigure</depend>
    <depend>eigen</depend>
    <depend>geometry_msgs</depend>
    <depend>nav_core</depend>
    <depend>nav_msgs</depend>
    <depend>pluginlib</depend>
    <depend>sensor_msgs</depend>
    <depend>std_msgs</depend>
    <depend>rosconsole</depend>
    <depend>roscpp</depend>
    <depend>rospy</depend>
    <depend>tf2</depend>
    <depend>tf2_ros</depend>
    <depend>visualization_msgs</depend>
    <depend>voxel_grid</depend>

    <exec_depend>message_runtime</exec_depend>

    <test_depend>rosunit</test_depend>
    <export>
        <nav_core plugin="${prefix}/blp_plugin.xml" />
    </export>
</package>
