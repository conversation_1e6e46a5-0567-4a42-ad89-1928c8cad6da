#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/eig3.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf.h
pf_vector.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_vector.h
pf_kdtree.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_kdtree.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_kdtree.h
rtk.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/rtk.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_pdf.h
pf_vector.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_vector.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_vector.h
stdio.h
-

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/eig3.c
math.h
-

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/pf.c
assert.h
-
math.h
-
stdlib.h
-
time.h
-
amcl/pf/pf.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/amcl/pf/pf.h
amcl/pf/pf_pdf.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/amcl/pf/pf_pdf.h
amcl/pf/pf_kdtree.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/amcl/pf/pf_kdtree.h
portable_utils.hpp
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/portable_utils.hpp
float.h
-

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/pf_draw.c
assert.h
-
math.h
-
stdlib.h
-
rtk.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/rtk.h
pf.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/pf.h
pf_pdf.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/pf_pdf.h
pf_kdtree.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/pf_kdtree.h

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/pf_kdtree.c
assert.h
-
math.h
-
stdlib.h
-
string.h
-
amcl/pf/pf_vector.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/amcl/pf/pf_vector.h
amcl/pf/pf_kdtree.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/amcl/pf/pf_kdtree.h

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/pf_pdf.c
assert.h
-
math.h
-
stdlib.h
-
string.h
-
amcl/pf/pf_pdf.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/amcl/pf/pf_pdf.h
portable_utils.hpp
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/portable_utils.hpp

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/pf_vector.c
math.h
-
amcl/pf/pf_vector.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/amcl/pf/pf_vector.h
amcl/pf/eig3.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/pf/amcl/pf/eig3.h

/home/<USER>/demo_ws/src/navigation/amcl/src/include/portable_utils.hpp
stdlib.h
-

