set(_CATKIN_CURRENT_PACKAGE "amcl")
set(amcl_VERSION "1.17.3")
set(amcl_MAINTAINER "<PERSON>!! <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>")
set(amcl_PACKAGE_FORMAT "2")
set(amcl_BUILD_DEPENDS "message_filters" "tf2_geometry_msgs" "diagnostic_updater" "dynamic_reconfigure" "geometry_msgs" "nav_msgs" "rosbag" "roscpp" "sensor_msgs" "std_srvs" "tf2" "tf2_msgs" "tf2_ros")
set(amcl_BUILD_EXPORT_DEPENDS "diagnostic_updater" "dynamic_reconfigure" "geometry_msgs" "nav_msgs" "rosbag" "roscpp" "sensor_msgs" "std_srvs" "tf2" "tf2_msgs" "tf2_ros")
set(amcl_BUILDTOOL_DEPENDS "catkin")
set(amcl_BUILDTOOL_EXPORT_DEPENDS )
set(amcl_EXEC_DEPENDS "diagnostic_updater" "dynamic_reconfigure" "geometry_msgs" "nav_msgs" "rosbag" "roscpp" "sensor_msgs" "std_srvs" "tf2" "tf2_msgs" "tf2_ros")
set(amcl_RUN_DEPENDS "diagnostic_updater" "dynamic_reconfigure" "geometry_msgs" "nav_msgs" "rosbag" "roscpp" "sensor_msgs" "std_srvs" "tf2" "tf2_msgs" "tf2_ros")
set(amcl_TEST_DEPENDS "map_server" "rostest" "python3-pykdl" "tf2_py")
set(amcl_DOC_DEPENDS )
set(amcl_URL_WEBSITE "http://wiki.ros.org/amcl")
set(amcl_URL_BUGTRACKER "")
set(amcl_URL_REPOSITORY "")
set(amcl_DEPRECATED "")