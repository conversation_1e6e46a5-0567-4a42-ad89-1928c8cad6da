set(_CATKIN_CURRENT_PACKAGE "base_local_planner")
set(base_local_planner_VERSION "1.17.3")
set(base_local_planner_MAINTAINER "<PERSON> <<EMAIL>>, <PERSON>! <<EMAIL>>, <PERSON> <<EMAIL>>")
set(base_local_planner_PACKAGE_FORMAT "3")
set(base_local_planner_BUILD_DEPENDS "cmake_modules" "message_generation" "tf2_geometry_msgs" "angles" "costmap_2d" "dynamic_reconfigure" "eigen" "geometry_msgs" "nav_core" "nav_msgs" "pluginlib" "sensor_msgs" "std_msgs" "rosconsole" "roscpp" "rospy" "tf2" "tf2_ros" "visualization_msgs" "voxel_grid")
set(base_local_planner_BUILD_EXPORT_DEPENDS "angles" "costmap_2d" "dynamic_reconfigure" "eigen" "geometry_msgs" "nav_core" "nav_msgs" "pluginlib" "sensor_msgs" "std_msgs" "rosconsole" "roscpp" "rospy" "tf2" "tf2_ros" "visualization_msgs" "voxel_grid")
set(base_local_planner_BUILDTOOL_DEPENDS "catkin" "python-setuptools" "python3-setuptools")
set(base_local_planner_BUILDTOOL_DEPENDS_catkin_VERSION_GTE "0.5.68")
set(base_local_planner_BUILDTOOL_EXPORT_DEPENDS )
set(base_local_planner_EXEC_DEPENDS "message_runtime" "angles" "costmap_2d" "dynamic_reconfigure" "eigen" "geometry_msgs" "nav_core" "nav_msgs" "pluginlib" "sensor_msgs" "std_msgs" "rosconsole" "roscpp" "rospy" "tf2" "tf2_ros" "visualization_msgs" "voxel_grid")
set(base_local_planner_RUN_DEPENDS "message_runtime" "angles" "costmap_2d" "dynamic_reconfigure" "eigen" "geometry_msgs" "nav_core" "nav_msgs" "pluginlib" "sensor_msgs" "std_msgs" "rosconsole" "roscpp" "rospy" "tf2" "tf2_ros" "visualization_msgs" "voxel_grid")
set(base_local_planner_TEST_DEPENDS "rosunit")
set(base_local_planner_DOC_DEPENDS )
set(base_local_planner_URL_WEBSITE "http://wiki.ros.org/base_local_planner")
set(base_local_planner_URL_BUGTRACKER "")
set(base_local_planner_URL_REPOSITORY "")
set(base_local_planner_DEPRECATED "")