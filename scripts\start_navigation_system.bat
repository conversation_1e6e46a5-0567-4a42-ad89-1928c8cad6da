@echo off
REM 差速履带扫雪车导航系统启动脚本 (Windows版本)
REM 使用方法: start_navigation_system.bat [模式]

setlocal enabledelayedexpansion

REM 设置颜色代码
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM 显示帮助信息
if "%1"=="help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help

echo %INFO% 差速履带扫雪车导航系统启动脚本
echo %INFO% ========================================

REM 检查ROS环境
echo %INFO% 检查ROS环境...
if "%ROS_DISTRO%"=="" (
    echo %ERROR% ROS环境未设置，请先设置ROS环境变量
    pause
    exit /b 1
)

if not exist "devel\setup.bat" (
    echo %ERROR% 工作空间未编译，请先运行 catkin_make
    pause
    exit /b 1
)

call devel\setup.bat
echo %SUCCESS% ROS环境检查完成 ^(ROS %ROS_DISTRO%^)

REM 根据参数选择启动模式
set "mode=%1"
if "%mode%"=="" set "mode=simulation"

if "%mode%"=="simulation" goto :start_simulation
if "%mode%"=="real" goto :start_real_robot
if "%mode%"=="test" goto :start_test_mode

echo %ERROR% 未知模式: %mode%
goto :show_help

:start_simulation
echo %INFO% 启动仿真模式...

REM 启动Gazebo仿真环境
echo %INFO% 启动Gazebo仿真环境...
start "Gazebo" cmd /k "call devel\setup.bat && roslaunch tutorials gazebo_world.launch"

timeout /t 5 /nobreak >nul

REM 启动机器人模型
echo %INFO% 启动机器人模型...
start "Robot" cmd /k "call devel\setup.bat && roslaunch tutorials robot.launch"

timeout /t 3 /nobreak >nul

REM 启动导航系统
call :start_navigation_core

REM 启动rviz可视化
echo %INFO% 启动rviz可视化...
start "RViz" cmd /k "call devel\setup.bat && timeout /t 5 /nobreak >nul && rviz"

goto :end

:start_real_robot
echo %INFO% 启动真实机器人模式...

REM 启动底盘驱动
echo %INFO% 启动底盘驱动...
start "Base Driver" cmd /k "call devel\setup.bat && echo 请手动启动底盘驱动节点"

REM 启动传感器
echo %INFO% 启动传感器...
start "Sensors" cmd /k "call devel\setup.bat && echo 请手动启动传感器节点"

timeout /t 3 /nobreak >nul

REM 启动定位
echo %INFO% 启动定位系统...
start "Localization" cmd /k "call devel\setup.bat && roslaunch navigation amcl.launch"

timeout /t 2 /nobreak >nul

REM 启动导航系统
call :start_navigation_core

goto :end

:start_navigation_core
echo %INFO% 启动导航核心模块...

REM 启动地图服务器
echo %INFO% 启动地图服务器...
start "Map Server" cmd /k "call devel\setup.bat && echo 请提供正确的地图文件路径 && pause"

timeout /t 2 /nobreak >nul

REM 启动全局路径规划器
echo %INFO% 启动A*全局路径规划器...
start "Global Planner" cmd /k "call devel\setup.bat && rosrun a_star_path_planning a_star_path_planner"

timeout /t 1 /nobreak >nul

REM 启动局部路径规划器
echo %INFO% 启动直线局部路径规划器...
start "Local Planner" cmd /k "call devel\setup.bat && rosrun straight_path_planner straight_path_planner"

timeout /t 1 /nobreak >nul

REM 启动Pure Pursuit控制器
echo %INFO% 启动Pure Pursuit控制器...
start "Controller" cmd /k "call devel\setup.bat && rosrun circle_control circle_control"

timeout /t 1 /nobreak >nul

echo %SUCCESS% 导航系统启动完成！
goto :eof

:start_test_mode
echo %INFO% 启动测试模式...

REM 启动roscore
echo %INFO% 启动ROS核心...
start "ROS Core" cmd /k "roscore"

timeout /t 3 /nobreak >nul

REM 测试A*路径规划器
echo %INFO% 测试A*路径规划器...
start "A* Test" cmd /k "call devel\setup.bat && echo 启动A*路径规划器测试... && rosrun a_star_path_planning a_star_path_planner"

REM 测试Frenet轨迹规划器
echo %INFO% 测试Frenet轨迹规划器...
start "Frenet Test" cmd /k "call devel\setup.bat && echo 启动Frenet轨迹规划器测试... && rosrun path_and_pose_subscriber main_test"

REM 测试控制器
echo %INFO% 测试Pure Pursuit控制器...
start "Controller Test" cmd /k "call devel\setup.bat && echo 启动控制器测试... && rosrun circle_control circle_control"

goto :end

:show_help
echo 差速履带扫雪车导航系统启动脚本
echo.
echo 使用方法:
echo   %0 [模式]
echo.
echo 模式选项:
echo   simulation  - 启动仿真模式 ^(默认^)
echo   real        - 启动真实机器人模式
echo   test        - 启动测试模式
echo   help        - 显示此帮助信息
echo.
echo 示例:
echo   %0 simulation   # 启动仿真模式
echo   %0 real         # 启动真实机器人模式
echo   %0 test         # 启动测试模式
goto :end

:end
echo %SUCCESS% 系统启动完成！
echo %INFO% 按任意键退出...
pause >nul
exit /b 0
