# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/demo_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/demo_ws/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/demo_ws/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/demo_ws/build/CMakeFiles /home/<USER>/demo_ws/build/circle_control/CMakeFiles/progress.marks
	cd /home/<USER>/demo_ws/build && $(MAKE) -f CMakeFiles/Makefile2 circle_control/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/demo_ws/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f CMakeFiles/Makefile2 circle_control/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/demo_ws/build && $(MAKE) -f CMakeFiles/Makefile2 circle_control/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f CMakeFiles/Makefile2 circle_control/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/demo_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
circle_control/CMakeFiles/circle_control_node.dir/rule:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f CMakeFiles/Makefile2 circle_control/CMakeFiles/circle_control_node.dir/rule
.PHONY : circle_control/CMakeFiles/circle_control_node.dir/rule

# Convenience name for target.
circle_control_node: circle_control/CMakeFiles/circle_control_node.dir/rule

.PHONY : circle_control_node

# fast build rule for target.
circle_control_node/fast:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/build
.PHONY : circle_control_node/fast

# Convenience name for target.
circle_control/CMakeFiles/circle_control.dir/rule:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f CMakeFiles/Makefile2 circle_control/CMakeFiles/circle_control.dir/rule
.PHONY : circle_control/CMakeFiles/circle_control.dir/rule

# Convenience name for target.
circle_control: circle_control/CMakeFiles/circle_control.dir/rule

.PHONY : circle_control

# fast build rule for target.
circle_control/fast:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control.dir/build.make circle_control/CMakeFiles/circle_control.dir/build
.PHONY : circle_control/fast

src/circle_control.o: src/circle_control.cc.o

.PHONY : src/circle_control.o

# target to build an object file
src/circle_control.cc.o:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/circle_control.cc.o
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control.dir/build.make circle_control/CMakeFiles/circle_control.dir/src/circle_control.cc.o
.PHONY : src/circle_control.cc.o

src/circle_control.i: src/circle_control.cc.i

.PHONY : src/circle_control.i

# target to preprocess a source file
src/circle_control.cc.i:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/circle_control.cc.i
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control.dir/build.make circle_control/CMakeFiles/circle_control.dir/src/circle_control.cc.i
.PHONY : src/circle_control.cc.i

src/circle_control.s: src/circle_control.cc.s

.PHONY : src/circle_control.s

# target to generate assembly for a file
src/circle_control.cc.s:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/circle_control.cc.s
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control.dir/build.make circle_control/CMakeFiles/circle_control.dir/src/circle_control.cc.s
.PHONY : src/circle_control.cc.s

src/main.o: src/main.cc.o

.PHONY : src/main.o

# target to build an object file
src/main.cc.o:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/main.cc.o
.PHONY : src/main.cc.o

src/main.i: src/main.cc.i

.PHONY : src/main.i

# target to preprocess a source file
src/main.cc.i:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/main.cc.i
.PHONY : src/main.cc.i

src/main.s: src/main.cc.s

.PHONY : src/main.s

# target to generate assembly for a file
src/main.cc.s:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/main.cc.s
.PHONY : src/main.cc.s

src/purpursuit.o: src/purpursuit.cc.o

.PHONY : src/purpursuit.o

# target to build an object file
src/purpursuit.cc.o:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/purpursuit.cc.o
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control.dir/build.make circle_control/CMakeFiles/circle_control.dir/src/purpursuit.cc.o
.PHONY : src/purpursuit.cc.o

src/purpursuit.i: src/purpursuit.cc.i

.PHONY : src/purpursuit.i

# target to preprocess a source file
src/purpursuit.cc.i:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/purpursuit.cc.i
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control.dir/build.make circle_control/CMakeFiles/circle_control.dir/src/purpursuit.cc.i
.PHONY : src/purpursuit.cc.i

src/purpursuit.s: src/purpursuit.cc.s

.PHONY : src/purpursuit.s

# target to generate assembly for a file
src/purpursuit.cc.s:
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control_node.dir/build.make circle_control/CMakeFiles/circle_control_node.dir/src/purpursuit.cc.s
	cd /home/<USER>/demo_ws/build && $(MAKE) -f circle_control/CMakeFiles/circle_control.dir/build.make circle_control/CMakeFiles/circle_control.dir/src/purpursuit.cc.s
.PHONY : src/purpursuit.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... circle_control_node"
	@echo "... circle_control"
	@echo "... src/circle_control.o"
	@echo "... src/circle_control.i"
	@echo "... src/circle_control.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/purpursuit.o"
	@echo "... src/purpursuit.i"
	@echo "... src/purpursuit.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/demo_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

