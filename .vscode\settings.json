{"python.autoComplete.extraPaths": ["/home/<USER>/demo_ws/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "python.analysis.extraPaths": ["/home/<USER>/demo_ws/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "files.associations": {"any": "cpp", "array": "cpp", "atomic": "cpp", "strstream": "cpp", "barrier": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "cfenv": "cpp", "charconv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cuchar": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "expected": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "netfwd": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "source_location": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "rope": "cpp", "slist": "cpp", "flat_map": "cpp", "flat_set": "cpp", "format": "cpp", "fstream": "cpp", "future": "cpp", "generator": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "latch": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "print": "cpp", "ranges": "cpp", "scoped_allocator": "cpp", "semaphore": "cpp", "shared_mutex": "cpp", "span": "cpp", "spanstream": "cpp", "sstream": "cpp", "stacktrace": "cpp", "stdexcept": "cpp", "stdfloat": "cpp", "stop_token": "cpp", "streambuf": "cpp", "syncstream": "cpp", "text_encoding": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp"}}