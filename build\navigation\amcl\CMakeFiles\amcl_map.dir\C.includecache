#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/map/map.h
stdint.h
-

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/map.c
assert.h
-
math.h
-
stdlib.h
-
string.h
-
stdio.h
-
amcl/map/map.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/amcl/map/map.h

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/map_draw.c
errno.h
-
math.h
-
stdlib.h
-
string.h
-
rtk.h
-
amcl/map/map.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/amcl/map/map.h

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/map_range.c
assert.h
-
math.h
-
string.h
-
stdlib.h
-
amcl/map/map.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/amcl/map/map.h

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/map_store.c
errno.h
-
math.h
-
stdio.h
-
stdlib.h
-
string.h
-
amcl/map/map.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/amcl/map/map.h

