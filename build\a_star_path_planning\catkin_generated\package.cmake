set(_CATKIN_CURRENT_PACKAGE "a_star_path_planning")
set(a_star_path_planning_VERSION "0.0.0")
set(a_star_path_planning_MAINTAINER "yj <<EMAIL>>")
set(a_star_path_planning_PACKAGE_FORMAT "2")
set(a_star_path_planning_BUILD_DEPENDS "geometry_msgs" "nav_msgs" "roscpp")
set(a_star_path_planning_BUILD_EXPORT_DEPENDS "geometry_msgs" "nav_msgs" "roscpp")
set(a_star_path_planning_BUILDTOOL_DEPENDS "catkin")
set(a_star_path_planning_BUILDTOOL_EXPORT_DEPENDS )
set(a_star_path_planning_EXEC_DEPENDS "geometry_msgs" "nav_msgs" "roscpp")
set(a_star_path_planning_RUN_DEPENDS "geometry_msgs" "nav_msgs" "roscpp")
set(a_star_path_planning_TEST_DEPENDS )
set(a_star_path_planning_DOC_DEPENDS )
set(a_star_path_planning_URL_WEBSITE "")
set(a_star_path_planning_URL_BUGTRACKER "")
set(a_star_path_planning_URL_REPOSITORY "")
set(a_star_path_planning_DEPRECATED "")