#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/map/map.h
stdint.h
-

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf.h
pf_vector.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_vector.h
pf_kdtree.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_kdtree.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_kdtree.h
rtk.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/rtk.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_pdf.h
pf_vector.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_vector.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_vector.h
stdio.h
-

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/sensors/amcl_laser.h
amcl_sensor.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/sensors/amcl_sensor.h
../map/map.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/map/map.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/sensors/amcl_odom.h
amcl_sensor.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/sensors/amcl_sensor.h
../pf/pf_pdf.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf_pdf.h

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/sensors/amcl_sensor.h
../pf/pf.h
/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/pf/pf.h

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/sensors/amcl_laser.cpp
sys/types.h
-
math.h
-
stdlib.h
-
assert.h
-
unistd.h
-
amcl/sensors/amcl_laser.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/sensors/amcl/sensors/amcl_laser.h

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/sensors/amcl_odom.cpp
algorithm
-
sys/types.h
-
math.h
-
amcl/sensors/amcl_odom.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/sensors/amcl/sensors/amcl_odom.h

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/sensors/amcl_sensor.cpp
amcl/sensors/amcl_sensor.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/sensors/amcl/sensors/amcl_sensor.h

