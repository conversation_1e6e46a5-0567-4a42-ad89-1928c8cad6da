#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/home/<USER>/demo_ws/devel:$CMAKE_PREFIX_PATH"
export LD_LIBRARY_PATH='/opt/ros/noetic/lib:/usr/local/cuda-11.4/lib64'
export PWD='/home/<USER>/demo_ws/build'
export ROSLISP_PACKAGE_DIRECTORIES='/home/<USER>/demo_ws/devel/share/common-lisp'
export ROS_PACKAGE_PATH="/home/<USER>/demo_ws/src:$ROS_PACKAGE_PATH"