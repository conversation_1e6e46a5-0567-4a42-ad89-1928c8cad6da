/usr/bin/c++ -fPIC   -shared -Wl,-soname,libbase_local_planner.so -o /home/<USER>/demo_ws/devel/lib/libbase_local_planner.so CMakeFiles/base_local_planner.dir/src/footprint_helper.cpp.o CMakeFiles/base_local_planner.dir/src/goal_functions.cpp.o CMakeFiles/base_local_planner.dir/src/map_cell.cpp.o CMakeFiles/base_local_planner.dir/src/map_grid.cpp.o CMakeFiles/base_local_planner.dir/src/map_grid_visualizer.cpp.o CMakeFiles/base_local_planner.dir/src/map_grid_cost_function.cpp.o CMakeFiles/base_local_planner.dir/src/latched_stop_rotate_controller.cpp.o CMakeFiles/base_local_planner.dir/src/local_planner_util.cpp.o CMakeFiles/base_local_planner.dir/src/odometry_helper_ros.cpp.o CMakeFiles/base_local_planner.dir/src/obstacle_cost_function.cpp.o CMakeFiles/base_local_planner.dir/src/oscillation_cost_function.cpp.o CMakeFiles/base_local_planner.dir/src/prefer_forward_cost_function.cpp.o CMakeFiles/base_local_planner.dir/src/point_grid.cpp.o CMakeFiles/base_local_planner.dir/src/costmap_model.cpp.o CMakeFiles/base_local_planner.dir/src/simple_scored_sampling_planner.cpp.o CMakeFiles/base_local_planner.dir/src/simple_trajectory_generator.cpp.o CMakeFiles/base_local_planner.dir/src/trajectory.cpp.o CMakeFiles/base_local_planner.dir/src/twirling_cost_function.cpp.o CMakeFiles/base_local_planner.dir/src/voxel_grid_model.cpp.o  -Wl,-rpath,/home/<USER>/demo_ws/devel/lib:/opt/ros/noetic/lib: /home/<USER>/demo_ws/devel/lib/liblayers.so /opt/ros/noetic/lib/libdynamic_reconfigure_config_init_mutex.so /opt/ros/noetic/lib/liblaser_geometry.so /opt/ros/noetic/lib/libtf.so /opt/ros/noetic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/aarch64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 -lorocos-kdl -lorocos-kdl /opt/ros/noetic/lib/libtf2_ros.so /opt/ros/noetic/lib/libactionlib.so /opt/ros/noetic/lib/libmessage_filters.so /opt/ros/noetic/lib/libtf2.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/aarch64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/librostime.so /usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/aarch64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/aarch64-linux-gnu/libconsole_bridge.so.0.4 /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0 /home/<USER>/demo_ws/devel/lib/libcostmap_2d.so /opt/ros/noetic/lib/libdynamic_reconfigure_config_init_mutex.so /opt/ros/noetic/lib/liblaser_geometry.so /opt/ros/noetic/lib/libtf.so /opt/ros/noetic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/aarch64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 -lorocos-kdl /opt/ros/noetic/lib/libtf2_ros.so /opt/ros/noetic/lib/libactionlib.so /opt/ros/noetic/lib/libmessage_filters.so /opt/ros/noetic/lib/libtf2.so /home/<USER>/demo_ws/devel/lib/libvoxel_grid.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/aarch64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/librostime.so /usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/aarch64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/aarch64-linux-gnu/libconsole_bridge.so.0.4 /usr/lib/aarch64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.71.0 -lpthread 
