/usr/bin/c++    -rdynamic CMakeFiles/base_local_planner_utest.dir/test/gtest_main.cpp.o CMakeFiles/base_local_planner_utest.dir/test/utest.cpp.o CMakeFiles/base_local_planner_utest.dir/test/velocity_iterator_test.cpp.o CMakeFiles/base_local_planner_utest.dir/test/footprint_helper_test.cpp.o CMakeFiles/base_local_planner_utest.dir/test/trajectory_generator_test.cpp.o CMakeFiles/base_local_planner_utest.dir/test/map_grid_test.cpp.o  -o /home/<USER>/demo_ws/devel/lib/base_local_planner/base_local_planner_utest   -L/home/<USER>/demo_ws/build/gtest  -Wl,-rpath,/home/<USER>/demo_ws/build/gtest:/home/<USER>/demo_ws/build/gtest/lib:/home/<USER>/demo_ws/devel/lib:/opt/ros/noetic/lib ../../gtest/lib/libgtest.so /home/<USER>/demo_ws/devel/lib/libtrajectory_planner_ros.so /home/<USER>/demo_ws/devel/lib/libbase_local_planner.so /home/<USER>/demo_ws/devel/lib/liblayers.so /home/<USER>/demo_ws/devel/lib/libcostmap_2d.so /usr/lib/aarch64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.71.0 /opt/ros/noetic/lib/libdynamic_reconfigure_config_init_mutex.so /opt/ros/noetic/lib/liblaser_geometry.so /opt/ros/noetic/lib/libtf.so /opt/ros/noetic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/aarch64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 -lorocos-kdl /opt/ros/noetic/lib/libtf2_ros.so /opt/ros/noetic/lib/libactionlib.so /opt/ros/noetic/lib/libmessage_filters.so /opt/ros/noetic/lib/libtf2.so /home/<USER>/demo_ws/devel/lib/libvoxel_grid.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/aarch64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/librostime.so /usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/aarch64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/aarch64-linux-gnu/libconsole_bridge.so.0.4 /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0 -lpthread /usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.71.0 
