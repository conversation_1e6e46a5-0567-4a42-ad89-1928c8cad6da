# 差速履带扫雪车规划控制模块 Makefile
# 适用于Windows和Linux环境

# 检测操作系统
ifeq ($(OS),Windows_NT)
    DETECTED_OS := Windows
    SHELL_EXT := .bat
    SETUP_CMD := call devel\setup.bat &&
else
    DETECTED_OS := Linux
    SHELL_EXT := .sh
    SETUP_CMD := source devel/setup.bash &&
endif

# 默认目标
.PHONY: all help build clean test start-sim start-real start-test

# 显示帮助信息
help:
	@echo "差速履带扫雪车规划控制模块构建系统"
	@echo "======================================"
	@echo ""
	@echo "可用目标:"
	@echo "  help        - 显示此帮助信息"
	@echo "  build       - 编译整个项目"
	@echo "  clean       - 清理编译文件"
	@echo "  test        - 运行所有测试"
	@echo "  test-astar  - 测试A*路径规划器"
	@echo "  test-control- 测试Pure Pursuit控制器"
	@echo "  start-sim   - 启动仿真模式"
	@echo "  start-real  - 启动真实机器人模式"
	@echo "  start-test  - 启动测试模式"
	@echo "  install-deps- 安装依赖包"
	@echo "  setup-env   - 设置环境"
	@echo ""
	@echo "当前操作系统: $(DETECTED_OS)"

# 默认目标
all: build

# 编译项目
build:
	@echo "编译差速履带扫雪车规划控制模块..."
ifeq ($(DETECTED_OS),Windows)
	catkin_make
else
	catkin_make
endif
	@echo "编译完成！"

# 清理编译文件
clean:
	@echo "清理编译文件..."
ifeq ($(DETECTED_OS),Windows)
	if exist build rmdir /s /q build
	if exist devel rmdir /s /q devel
else
	rm -rf build devel
endif
	@echo "清理完成！"

# 安装依赖包
install-deps:
	@echo "安装依赖包..."
ifeq ($(DETECTED_OS),Windows)
	@echo "请手动安装以下依赖包:"
	@echo "- ROS Melodic/Noetic"
	@echo "- ros-navigation"
	@echo "- libeigen3-dev"
else
	sudo apt-get update
	sudo apt-get install -y ros-$(ROS_DISTRO)-navigation
	sudo apt-get install -y ros-$(ROS_DISTRO)-map-server
	sudo apt-get install -y ros-$(ROS_DISTRO)-amcl
	sudo apt-get install -y libeigen3-dev
	rosdep install --from-paths src --ignore-src -r -y
endif
	@echo "依赖包安装完成！"

# 设置环境
setup-env:
	@echo "设置环境变量..."
ifeq ($(DETECTED_OS),Windows)
	call devel\setup.bat
else
	@echo "请运行: source devel/setup.bash"
endif

# 启动仿真模式
start-sim:
	@echo "启动仿真模式..."
ifeq ($(DETECTED_OS),Windows)
	scripts\start_navigation_system.bat simulation
else
	./scripts/start_navigation_system.sh simulation
endif

# 启动真实机器人模式
start-real:
	@echo "启动真实机器人模式..."
ifeq ($(DETECTED_OS),Windows)
	scripts\start_navigation_system.bat real
else
	./scripts/start_navigation_system.sh real
endif

# 启动测试模式
start-test:
	@echo "启动测试模式..."
ifeq ($(DETECTED_OS),Windows)
	scripts\start_navigation_system.bat test
else
	./scripts/start_navigation_system.sh test
endif

# 运行所有测试
test:
	@echo "运行所有模块测试..."
ifeq ($(DETECTED_OS),Windows)
	@echo "请手动运行测试脚本"
	@echo "Windows环境下请使用ROS命令行进行测试"
else
	./scripts/test_modules.sh all
endif

# 测试A*路径规划器
test-astar:
	@echo "测试A*路径规划器..."
ifeq ($(DETECTED_OS),Windows)
	$(SETUP_CMD) rosrun a_star_path_planning a_star_path_planner
else
	./scripts/test_modules.sh astar
endif

# 测试控制器
test-control:
	@echo "测试Pure Pursuit控制器..."
ifeq ($(DETECTED_OS),Windows)
	$(SETUP_CMD) rosrun circle_control circle_control
else
	./scripts/test_modules.sh control
endif

# 快速编译和测试
quick-test: build
	@echo "快速编译和测试..."
	$(MAKE) test-astar

# 完整构建流程
full-build: clean install-deps build test
	@echo "完整构建流程完成！"

# 开发者模式 - 持续编译
dev:
	@echo "开发者模式 - 监控文件变化并自动编译"
ifeq ($(DETECTED_OS),Linux)
	while inotifywait -r -e modify,create,delete src/; do \
		echo "检测到文件变化，重新编译..."; \
		$(MAKE) build; \
	done
else
	@echo "开发者模式仅在Linux下支持"
endif

# 生成文档
docs:
	@echo "生成项目文档..."
	@echo "技术文档已生成: 差速履带扫雪车规划控制模块技术文档.md"
	@echo "README文档: README.md"

# 检查代码风格
lint:
	@echo "检查代码风格..."
ifeq ($(DETECTED_OS),Linux)
	find src/ -name "*.cpp" -o -name "*.h" | xargs clang-format -i
	@echo "代码格式化完成"
else
	@echo "代码风格检查仅在Linux下支持"
endif

# 创建发布包
package:
	@echo "创建发布包..."
	tar -czf sxcar_navigation_$(shell date +%Y%m%d).tar.gz \
		src/ \
		scripts/ \
		CMakeLists.txt \
		package.xml \
		README.md \
		差速履带扫雪车规划控制模块技术文档.md
	@echo "发布包创建完成: sxcar_navigation_$(shell date +%Y%m%d).tar.gz"

# 部署到机器人
deploy:
	@echo "部署到机器人..."
	@echo "请确保已配置SSH密钥和目标机器人IP"
	# rsync -avz --exclude='build/' --exclude='devel/' . robot@ROBOT_IP:~/sxcar_ws/
	@echo "部署功能需要配置具体的机器人连接信息"

# 备份项目
backup:
	@echo "备份项目..."
	mkdir -p backups
	tar -czf backups/sxcar_backup_$(shell date +%Y%m%d_%H%M%S).tar.gz \
		src/ scripts/ *.md Makefile
	@echo "备份完成: backups/sxcar_backup_$(shell date +%Y%m%d_%H%M%S).tar.gz"

# 显示项目状态
status:
	@echo "项目状态检查..."
	@echo "================================"
	@echo "操作系统: $(DETECTED_OS)"
	@echo "ROS版本: $(ROS_DISTRO)"
	@echo "工作空间: $(PWD)"
	@echo ""
	@echo "编译状态:"
ifeq ($(wildcard build/),)
	@echo "  ❌ 未编译"
else
	@echo "  ✅ 已编译"
endif
	@echo ""
	@echo "主要模块:"
	@echo "  - A*路径规划: src/a_star_path_planning/"
	@echo "  - 直线规划: src/straight_path_planner/"
	@echo "  - Frenet规划: src/path_and_pose_subscriber/"
	@echo "  - Pure Pursuit控制: src/circle_control/"
	@echo "  - ROS导航: src/navigation/"

# 清理日志文件
clean-logs:
	@echo "清理日志文件..."
ifeq ($(DETECTED_OS),Windows)
	if exist logs rmdir /s /q logs
else
	rm -rf ~/.ros/log/*
endif
	@echo "日志清理完成！"
