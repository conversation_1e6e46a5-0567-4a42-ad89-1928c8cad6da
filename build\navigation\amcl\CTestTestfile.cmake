# CMake generated Testfile for 
# Source directory: /home/<USER>/demo_ws/src/navigation/amcl
# Build directory: /home/<USER>/demo_ws/build/navigation/amcl
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(_ctest_amcl_rostest_test_set_initial_pose.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_set_initial_pose.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_set_initial_pose.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/set_initial_pose.xml ")
set_tests_properties(_ctest_amcl_rostest_test_set_initial_pose.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;159;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
add_test(_ctest_amcl_rostest_test_set_initial_pose_delayed.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_set_initial_pose_delayed.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_set_initial_pose_delayed.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/set_initial_pose_delayed.xml ")
set_tests_properties(_ctest_amcl_rostest_test_set_initial_pose_delayed.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;160;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
add_test(_ctest_amcl_rostest_test_basic_localization_stage.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_basic_localization_stage.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_basic_localization_stage.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/basic_localization_stage.xml ")
set_tests_properties(_ctest_amcl_rostest_test_basic_localization_stage.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;161;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
add_test(_ctest_amcl_rostest_test_global_localization_stage.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_global_localization_stage.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_global_localization_stage.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/global_localization_stage.xml ")
set_tests_properties(_ctest_amcl_rostest_test_global_localization_stage.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;162;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
add_test(_ctest_amcl_rostest_test_small_loop_prf.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_small_loop_prf.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_small_loop_prf.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/small_loop_prf.xml ")
set_tests_properties(_ctest_amcl_rostest_test_small_loop_prf.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;163;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
add_test(_ctest_amcl_rostest_test_small_loop_crazy_driving_prg.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_small_loop_crazy_driving_prg.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_small_loop_crazy_driving_prg.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/small_loop_crazy_driving_prg.xml ")
set_tests_properties(_ctest_amcl_rostest_test_small_loop_crazy_driving_prg.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;164;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
add_test(_ctest_amcl_rostest_test_texas_greenroom_loop.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_texas_greenroom_loop.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_texas_greenroom_loop.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/texas_greenroom_loop.xml ")
set_tests_properties(_ctest_amcl_rostest_test_texas_greenroom_loop.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;165;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
add_test(_ctest_amcl_rostest_test_rosie_multilaser.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_rosie_multilaser.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_rosie_multilaser.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/rosie_multilaser.xml ")
set_tests_properties(_ctest_amcl_rostest_test_rosie_multilaser.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;166;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
add_test(_ctest_amcl_rostest_test_texas_willow_hallway_loop.xml "/home/<USER>/demo_ws/build/catkin_generated/env_cached.sh" "/usr/bin/python3" "/opt/ros/noetic/share/catkin/cmake/test/run_tests.py" "/home/<USER>/demo_ws/build/test_results/amcl/rostest-test_texas_willow_hallway_loop.xml" "--return-code" "/usr/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/home/<USER>/demo_ws/src/navigation/amcl --package=amcl --results-filename test_texas_willow_hallway_loop.xml --results-base-dir \"/home/<USER>/demo_ws/build/test_results\" /home/<USER>/demo_ws/src/navigation/amcl/test/texas_willow_hallway_loop.xml ")
set_tests_properties(_ctest_amcl_rostest_test_texas_willow_hallway_loop.xml PROPERTIES  _BACKTRACE_TRIPLES "/opt/ros/noetic/share/catkin/cmake/test/tests.cmake;160;add_test;/opt/ros/noetic/share/rostest/cmake/rostest-extras.cmake;52;catkin_run_tests_target;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;167;add_rostest;/home/<USER>/demo_ws/src/navigation/amcl/CMakeLists.txt;0;")
