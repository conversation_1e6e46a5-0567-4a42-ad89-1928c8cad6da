# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile C with /usr/bin/cc
C_FLAGS = -fPIC  

C_DEFINES = -DHAVE_DRAND48 -DHAVE_UNISTD_H -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"amcl\" -Damcl_pf_EXPORTS

C_INCLUDES = -I/home/<USER>/demo_ws/devel/include -I/home/<USER>/demo_ws/src/navigation/amcl/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/usr/include/eigen3 -I/home/<USER>/demo_ws/src/navigation/amcl/src/include 

