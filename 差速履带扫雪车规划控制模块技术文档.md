# 差速履带扫雪车规划控制模块技术文档

## 1. 系统概述

### 1.1 项目简介
本项目是一个基于ROS的差速履带扫雪车自主导航系统，采用分层架构设计，实现了从全局路径规划到局部轨迹跟踪的完整导航解决方案。

### 1.2 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   感知层        │    │   规划层        │    │   控制层        │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • 激光雷达      │    │ • 全局路径规划  │    │ • Pure Pursuit  │
│ • 里程计        │ -> │ • 局部路径规划  │ -> │ • 运动控制      │
│ • 代价地图      │    │ • 轨迹优化      │    │ • 安全监控      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.3 技术特点
- **多层次规划**: 全局-局部-控制三层架构
- **多算法集成**: A*、Dijkstra、DWA、Frenet等算法
- **实时性**: 支持动态环境下的实时规划
- **安全性**: 完善的急停和故障处理机制
- **模块化**: 各功能模块独立，便于维护扩展

## 2. 核心功能模块

### 2.1 全局路径规划模块 (navigation/global_planner)

**功能描述**: 基于全局地图进行长距离路径规划

**核心算法**:
- Dijkstra算法 (默认)
- A*算法

**主要接口**:
- 输入: `/grid_map` (地图), `/move_base_simple/goal` (目标点)
- 输出: `/path` (全局路径)

### 2.2 A*路径规划模块 (a_star_path_planning)

**功能描述**: 实现A*算法的高效路径规划

**特点**:
- 支持全局和局部两种模式
- 障碍物膨胀处理
- 启发式搜索优化

**主要接口**:
- 输入: `/grid_map`, `/amcl_pose`, `/move_base_simple/goal`
- 输出: `/path`

### 2.3 直线路径规划模块 (straight_path_planner)

**功能描述**: 生成直线路径，适用于简单场景

**特点**:
- 基于代价地图的障碍物检测
- 实时路径重规划
- 轻量化设计

**主要接口**:
- 输入: `/move_base/local_costmap/costmap`
- 输出: `/local_path`

### 2.4 Frenet轨迹优化模块 (path_and_pose_subscriber)

**功能描述**: 基于Frenet坐标系的最优轨迹规划

**核心算法**:
- 五次多项式轨迹生成
- 多候选轨迹评估
- 代价函数优化

**特点**:
- 横向和纵向解耦规划
- 动态障碍物避障
- 平滑轨迹生成

### 2.5 Pure Pursuit控制模块 (circle_control)

**功能描述**: 基于Pure Pursuit算法的路径跟踪控制

**核心功能**:
- 路径跟踪控制
- 差速履带运动学
- 安全监控机制

**主要接口**:
- 输入: `/odom`, `/local_path`, `/TaskCtl`, `/EStop`
- 输出: `/cmd_vel`

### 2.6 DWA局部规划模块 (navigation/dwa_local_planner)

**功能描述**: 动态窗口法局部路径规划

**特点**:
- 考虑动力学约束
- 实时避障
- 速度优化

## 3. 系统数据流

```mermaid
graph TD
    A[全局地图/grid_map] --> B[全局路径规划器]
    B --> |/path| C[局部路径规划器]
    C --> |/local_path| D[轨迹优化器]
    D --> E[Pure Pursuit控制器]
    E --> |/cmd_vel| F[差速履带车]
    
    G[激光雷达] --> H[局部代价地图]
    H --> C
    H --> D
    
    I[里程计/odom] --> E
    J[遥控器/TaskCtl] --> E
    K[急停/EStop] --> E
    
    F --> L[执行器]
```

## 4. 主要话题接口

| 话题名称 | 消息类型 | 功能描述 | 发布者 | 订阅者 |
|---------|---------|---------|--------|--------|
| `/grid_map` | nav_msgs/OccupancyGrid | 全局地图数据 | map_server | 路径规划器 |
| `/odom` | nav_msgs/Odometry | 机器人里程计 | 底盘驱动 | 控制器 |
| `/path` | nav_msgs/Path | 全局规划路径 | 全局规划器 | 局部规划器 |
| `/local_path` | nav_msgs/Path | 局部规划路径 | 局部规划器 | 控制器 |
| `/cmd_vel` | geometry_msgs/Twist | 速度控制指令 | 控制器 | 底盘驱动 |
| `/move_base_simple/goal` | geometry_msgs/PoseStamped | 目标点 | rviz/用户 | 规划器 |
| `/TaskCtl` | std_msgs/Int64 | 任务控制指令 | 遥控器 | 控制器 |
| `/EStop` | std_msgs/Int64 | 急停指令 | 遥控器 | 控制器 |
| `/heartbeat` | std_msgs/Int64 | 心跳信号 | 遥控器 | 控制器 |

## 5. 编译与环境配置

### 5.1 系统要求
- Ubuntu 18.04/20.04
- ROS Melodic/Noetic
- C++14 或更高版本
- Eigen3 库

### 5.2 编译步骤
```bash
# 进入工作空间
cd ~/demo_ws

# 编译所有包
catkin_make

# 或者使用catkin build (推荐)
catkin build

# 设置环境变量
source devel/setup.bash
```

### 5.3 依赖包安装
```bash
# 安装ROS导航包
sudo apt-get install ros-$ROS_DISTRO-navigation
sudo apt-get install ros-$ROS_DISTRO-map-server
sudo apt-get install ros-$ROS_DISTRO-amcl

# 安装Eigen3
sudo apt-get install libeigen3-dev

# 安装其他依赖
sudo apt-get install ros-$ROS_DISTRO-tf2-geometry-msgs
sudo apt-get install ros-$ROS_DISTRO-geometry-msgs
```

## 6. 各算法节点启动与测试方法

### 6.1 全局路径规划器 (Global Planner)

#### 启动方法
```bash
# 方法1: 单独启动
rosrun global_planner planner_node

# 方法2: 通过move_base启动 (推荐)
roslaunch navigation move_base.launch
```

#### 测试方法
```bash
# 1. 启动地图服务器
rosrun map_server map_server your_map.yaml

# 2. 启动全局规划器
rosrun global_planner planner_node

# 3. 发布目标点进行测试
rostopic pub /move_base_simple/goal geometry_msgs/PoseStamped "
header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: 'map'
pose:
  position:
    x: 5.0
    y: 3.0
    z: 0.0
  orientation:
    x: 0.0
    y: 0.0
    z: 0.0
    w: 1.0"

# 4. 查看规划结果
rostopic echo /path
```

#### 参数配置
```bash
# 在launch文件中配置参数
<param name="use_dijkstra" value="true"/>
<param name="use_grid_path" value="false"/>
<param name="allow_unknown" value="true"/>
```

### 6.2 A*路径规划器

#### 启动方法
```bash
# 启动全局A*规划器
rosrun a_star_path_planning a_star_path_planner

# 启动局部A*规划器
rosrun a_star_path_planning local_path
```

#### 测试方法
```bash
# 1. 启动必要的节点
roscore
rosrun map_server map_server your_map.yaml

# 2. 启动A*规划器
rosrun a_star_path_planning a_star_path_planner

# 3. 发布起点 (如果使用amcl_pose)
rostopic pub /amcl_pose geometry_msgs/PoseWithCovarianceStamped "
header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: 'map'
pose:
  pose:
    position:
      x: 0.0
      y: 0.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
  covariance: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]"

# 4. 发布目标点
rostopic pub /move_base_simple/goal geometry_msgs/PoseStamped "
header:
  frame_id: 'map'
pose:
  position:
    x: 10.0
    y: 5.0
    z: 0.0
  orientation:
    w: 1.0"

# 5. 查看规划路径
rostopic echo /path
rviz  # 可视化查看路径
```

#### 调试方法
```bash
# 查看节点状态
rosnode info /a_star_path_planner

# 监控话题
rostopic hz /path
rostopic hz /grid_map

# 查看日志
rosrun rqt_console rqt_console
```

### 6.3 直线路径规划器

#### 启动方法
```bash
# 启动直线路径规划器
rosrun straight_path_planner straight_path_planner

# 或启动定时规划器
rosrun straight_path_planner timer_plan
```

#### 测试方法
```bash
# 1. 启动代价地图
roslaunch navigation move_base.launch

# 2. 启动直线规划器
rosrun straight_path_planner straight_path_planner

# 3. 查看生成的局部路径
rostopic echo /local_path

# 4. 在rviz中可视化
rviz
# 添加Path显示，话题设置为/local_path
```

#### 参数调整
```bash
# 通过参数服务器设置
rosparam set /straight_path_planner/step_length 0.1
rosparam set /straight_path_planner/max_length 2.0
rosparam set /straight_path_planner/cost_threshold 50
```

### 6.4 Frenet轨迹优化器

#### 启动方法
```bash
# 启动Frenet规划器
rosrun path_and_pose_subscriber path_and_pose_subscriber

# 或运行测试程序
rosrun path_and_pose_subscriber main_test
```

#### 测试方法
```bash
# 1. 编译并运行测试
cd ~/demo_ws
catkin_make
rosrun path_and_pose_subscriber main_test

# 2. 查看输出结果
# 程序会输出轨迹规划的详细信息和性能指标

# 3. 可视化测试 (如果有ROS接口)
rostopic echo /frenet_path
```

#### 参数配置
在代码中可以调整以下参数：
```cpp
constexpr double MAX_SPEED = 50.0 / 3.6;      // 最大速度
constexpr double MAX_ACCEL = 2.0;             // 最大加速度
constexpr double MAX_CURVATURE = 1.0;         // 最大曲率
constexpr double TARGET_SPEED = 30.0 / 3.6;   // 目标速度
constexpr double ROBOT_RADIUS = 2.0;          // 机器人半径
```

### 6.5 Pure Pursuit控制器

#### 启动方法
```bash
# 启动Pure Pursuit控制器
rosrun circle_control circle_control
```

#### 测试方法
```bash
# 1. 启动里程计发布器 (模拟或真实)
rosrun tf2_ros static_transform_publisher 0 0 0 0 0 0 map odom
rosrun fake_localization fake_localization

# 2. 启动控制器
rosrun circle_control circle_control

# 3. 发布测试路径
rostopic pub /local_path nav_msgs/Path "
header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: 'map'
poses:
- header:
    seq: 0
    stamp:
      secs: 0
      nsecs: 0
    frame_id: 'map'
  pose:
    position:
      x: 0.0
      y: 0.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
- header:
    seq: 1
    stamp:
      secs: 0
      nsecs: 0
    frame_id: 'map'
  pose:
    position:
      x: 5.0
      y: 0.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0"

# 4. 启动任务 (发送启动指令)
rostopic pub /TaskCtl std_msgs/Int64 "data: 1"

# 5. 查看控制输出
rostopic echo /cmd_vel

# 6. 紧急停止测试
rostopic pub /EStop std_msgs/Int64 "data: 1"
```

#### 控制状态说明
```bash
# follow_state状态含义:
# 0: 未初始化，慢速进入起点
# 1: 作业中，正常跟踪路径
# 4: 停止状态
```

### 6.6 DWA局部规划器

#### 启动方法
```bash
# 通过move_base启动DWA
roslaunch navigation move_base.launch

# 确保在move_base配置中使用DWA
# base_local_planner: "dwa_local_planner/DWAPlannerROS"
```

#### 测试方法
```bash
# 1. 启动完整导航系统
roslaunch navigation move_base.launch

# 2. 启动地图和定位
rosrun map_server map_server your_map.yaml
roslaunch navigation amcl.launch

# 3. 发布导航目标
rostopic pub /move_base_simple/goal geometry_msgs/PoseStamped "
header:
  frame_id: 'map'
pose:
  position:
    x: 3.0
    y: 2.0
    z: 0.0
  orientation:
    w: 1.0"

# 4. 监控DWA输出
rostopic echo /cmd_vel
rostopic echo /move_base/DWAPlannerROS/local_plan
```

## 7. 系统集成测试

### 7.1 完整系统启动
```bash
# 创建启动脚本 start_navigation.launch
roslaunch tutorials simulation_robot.launch  # 启动仿真环境
roslaunch navigation move_base.launch        # 启动导航系统
rosrun circle_control circle_control         # 启动控制器
```

### 7.2 仿真环境测试
```bash
# 1. 启动Gazebo仿真
roslaunch tutorials gazebo_world.launch

# 2. 启动机器人模型
roslaunch tutorials robot.launch

# 3. 启动导航系统
roslaunch navigation move_base.launch

# 4. 启动rviz可视化
rviz -d navigation.rviz
```

### 7.3 性能监控
```bash
# 监控系统性能
rostopic hz /cmd_vel          # 控制频率
rostopic hz /local_path       # 路径更新频率
rostopic hz /odom            # 里程计频率

# 查看计算负载
htop
rosrun rqt_top rqt_top

# 监控话题延迟
rosrun rqt_graph rqt_graph
```

## 8. 常见问题与解决方案

### 8.1 编译问题
```bash
# 问题: 找不到Eigen3
# 解决:
sudo apt-get install libeigen3-dev
# 或在CMakeLists.txt中添加:
find_package(Eigen3 REQUIRED)

# 问题: ROS包依赖缺失
# 解决:
rosdep install --from-paths src --ignore-src -r -y
```

### 8.2 运行时问题
```bash
# 问题: 话题无数据
# 检查:
rostopic list
rostopic info /topic_name
rosnode list

# 问题: 路径规划失败
# 检查:
# 1. 地图是否正确加载
# 2. 起点终点是否在可行区域
# 3. 代价地图是否正常更新

# 问题: 控制器无响应
# 检查:
# 1. 里程计数据是否正常
# 2. 路径数据是否有效
# 3. 急停状态是否激活
```

### 8.3 调试工具
```bash
# 使用rviz可视化
rviz

# 查看话题数据
rostopic echo /topic_name

# 录制和回放数据
rosbag record -a
rosbag play your_bag.bag

# 性能分析
rosrun rqt_plot rqt_plot
rosrun rqt_console rqt_console
```

## 9. 参数调优指南

### 9.1 路径规划参数
```yaml
# global_planner参数
use_dijkstra: true
use_grid_path: false
allow_unknown: true

# A*参数
robot_radius: 0.2
inflation_radius: 0.3
```

### 9.2 控制器参数
```yaml
# Pure Pursuit参数
step_length: 0.1
max_length: 2.0
cost_threshold: 50
lookahead_distance: 1.75

# DWA参数
max_vel_x: 0.8
min_vel_x: 0.1
max_vel_theta: 1.0
acc_lim_x: 2.0
acc_lim_theta: 3.0
```

### 9.3 安全参数
```yaml
# 安全距离
robot_radius: 2.0
safety_margin: 0.5

# 速度限制
max_speed: 0.8  # m/s
emergency_stop_distance: 1.0  # m
```

## 10. 扩展开发指南

### 10.1 添加新的规划算法
1. 在相应模块下创建新的算法类
2. 继承基础接口类
3. 实现核心规划函数
4. 添加ROS接口封装
5. 更新CMakeLists.txt和package.xml

### 10.2 集成新传感器
1. 创建传感器数据处理节点
2. 定义数据接口和消息类型
3. 在相关模块中添加传感器数据处理
4. 更新代价地图生成逻辑

### 10.3 优化建议
- 使用多线程提高实时性
- 添加预测控制算法
- 集成机器学习方法
- 增强故障诊断能力

---

**文档版本**: v1.0
**最后更新**: 2025-07-10
**维护者**: 扫雪车项目组
