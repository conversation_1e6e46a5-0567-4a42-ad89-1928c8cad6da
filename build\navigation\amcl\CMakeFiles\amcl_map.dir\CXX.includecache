#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/demo_ws/src/navigation/amcl/include/amcl/map/map.h
stdint.h
-

/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/map_cspace.cpp
queue
-
math.h
-
stdlib.h
-
string.h
-
amcl/map/map.h
/home/<USER>/demo_ws/src/navigation/amcl/src/amcl/map/amcl/map/map.h

