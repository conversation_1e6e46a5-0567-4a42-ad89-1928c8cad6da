#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/usr/src/googletest/googletest/include/gtest/gtest-death-test.h
gtest/internal/gtest-death-test-internal.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-death-test-internal.h

/usr/src/googletest/googletest/include/gtest/gtest-matchers.h
memory
-
ostream
-
string
-
type_traits
-
gtest/gtest-printers.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest-printers.h
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-port.h

/usr/src/googletest/googletest/include/gtest/gtest-message.h
limits
-
memory
-
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-port.h

/usr/src/googletest/googletest/include/gtest/gtest-param-test.h
iterator
-
utility
-
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-param-util.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-param-util.h
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-port.h

/usr/src/googletest/googletest/include/gtest/gtest-printers.h
functional
-
ostream
-
sstream
-
string
-
tuple
-
type_traits
-
utility
-
vector
-
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-port.h
absl/strings/string_view.h
/usr/src/googletest/googletest/include/gtest/absl/strings/string_view.h
absl/types/optional.h
/usr/src/googletest/googletest/include/gtest/absl/types/optional.h
absl/types/variant.h
/usr/src/googletest/googletest/include/gtest/absl/types/variant.h
gtest/internal/custom/gtest-printers.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/custom/gtest-printers.h

/usr/src/googletest/googletest/include/gtest/gtest-spi.h
gtest/gtest.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest.h

/usr/src/googletest/googletest/include/gtest/gtest-test-part.h
iosfwd
-
vector
-
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-string.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-string.h

/usr/src/googletest/googletest/include/gtest/gtest-typed-test.h
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-port.h
gtest/internal/gtest-type-util.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-type-util.h

/usr/src/googletest/googletest/include/gtest/gtest.h
cstddef
-
limits
-
memory
-
ostream
-
type_traits
-
vector
-
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-string.h
/usr/src/googletest/googletest/include/gtest/gtest/internal/gtest-string.h
gtest/gtest-death-test.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest-death-test.h
gtest/gtest-matchers.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest-matchers.h
gtest/gtest-message.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest-message.h
gtest/gtest-param-test.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest-param-test.h
gtest/gtest-printers.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest-printers.h
gtest/gtest_prod.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest_prod.h
gtest/gtest-test-part.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest-test-part.h
gtest/gtest-typed-test.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest-typed-test.h
gtest/gtest_pred_impl.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest_pred_impl.h

/usr/src/googletest/googletest/include/gtest/gtest_pred_impl.h
gtest/gtest.h
/usr/src/googletest/googletest/include/gtest/gtest/gtest.h

/usr/src/googletest/googletest/include/gtest/gtest_prod.h

/usr/src/googletest/googletest/include/gtest/internal/custom/gtest-port.h

/usr/src/googletest/googletest/include/gtest/internal/custom/gtest-printers.h

/usr/src/googletest/googletest/include/gtest/internal/custom/gtest.h

/usr/src/googletest/googletest/include/gtest/internal/gtest-death-test-internal.h
gtest/gtest-matchers.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/gtest-matchers.h
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-internal.h
stdio.h
-
memory
-

/usr/src/googletest/googletest/include/gtest/internal/gtest-filepath.h
gtest/internal/gtest-string.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-string.h

/usr/src/googletest/googletest/include/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-port.h
stdlib.h
-
sys/types.h
-
sys/wait.h
-
unistd.h
-
stdexcept
-
ctype.h
-
float.h
-
string.h
-
iomanip
-
limits
-
map
-
set
-
string
-
type_traits
-
vector
-
gtest/gtest-message.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/gtest-message.h
gtest/internal/gtest-filepath.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-filepath.h
gtest/internal/gtest-string.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-string.h
gtest/internal/gtest-type-util.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-type-util.h

/usr/src/googletest/googletest/include/gtest/internal/gtest-param-util.h
ctype.h
-
cassert
-
iterator
-
memory
-
set
-
tuple
-
utility
-
vector
-
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-port.h
gtest/gtest-printers.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/gtest-printers.h

/usr/src/googletest/googletest/include/gtest/internal/gtest-port-arch.h
winapifamily.h
-

/usr/src/googletest/googletest/include/gtest/internal/gtest-port.h
ctype.h
-
stddef.h
-
stdio.h
-
stdlib.h
-
string.h
-
memory
-
type_traits
-
sys/types.h
-
sys/stat.h
-
AvailabilityMacros.h
-
TargetConditionals.h
-
algorithm
-
iostream
-
sstream
-
string
-
tuple
-
utility
-
vector
-
gtest/internal/gtest-port-arch.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-port-arch.h
gtest/internal/custom/gtest-port.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/custom/gtest-port.h
direct.h
-
io.h
-
unistd.h
-
strings.h
-
android/api-level.h
-
regex.h
-
typeinfo
-
pthread.h
-
time.h
-

/usr/src/googletest/googletest/include/gtest/internal/gtest-string.h
mem.h
-
string.h
-
string
-
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-port.h

/usr/src/googletest/googletest/include/gtest/internal/gtest-type-util.h
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/include/gtest/internal/gtest/internal/gtest-port.h
cxxabi.h
-
acxx_demangle.h
-

/usr/src/googletest/googletest/src/gtest-all.cc
gtest/gtest.h
/usr/src/googletest/googletest/src/gtest/gtest.h
src/gtest.cc
/usr/src/googletest/googletest/src/src/gtest.cc
src/gtest-death-test.cc
/usr/src/googletest/googletest/src/src/gtest-death-test.cc
src/gtest-filepath.cc
/usr/src/googletest/googletest/src/src/gtest-filepath.cc
src/gtest-matchers.cc
/usr/src/googletest/googletest/src/src/gtest-matchers.cc
src/gtest-port.cc
/usr/src/googletest/googletest/src/src/gtest-port.cc
src/gtest-printers.cc
/usr/src/googletest/googletest/src/src/gtest-printers.cc
src/gtest-test-part.cc
/usr/src/googletest/googletest/src/src/gtest-test-part.cc
src/gtest-typed-test.cc
/usr/src/googletest/googletest/src/src/gtest-typed-test.cc

/usr/src/googletest/googletest/src/gtest-death-test.cc
gtest/gtest-death-test.h
/usr/src/googletest/googletest/src/gtest/gtest-death-test.h
utility
-
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-port.h
gtest/internal/custom/gtest.h
/usr/src/googletest/googletest/src/gtest/internal/custom/gtest.h
crt_externs.h
-
errno.h
-
fcntl.h
-
limits.h
-
signal.h
-
stdarg.h
-
windows.h
-
sys/mman.h
-
sys/wait.h
-
spawn.h
-
lib/fdio/fd.h
-
lib/fdio/io.h
-
lib/fdio/spawn.h
-
lib/zx/channel.h
-
lib/zx/port.h
-
lib/zx/process.h
-
lib/zx/socket.h
-
zircon/processargs.h
-
zircon/syscalls.h
-
zircon/syscalls/policy.h
-
zircon/syscalls/port.h
-
gtest/gtest-message.h
/usr/src/googletest/googletest/src/gtest/gtest-message.h
gtest/internal/gtest-string.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-string.h
src/gtest-internal-inl.h
/usr/src/googletest/googletest/src/src/gtest-internal-inl.h

/usr/src/googletest/googletest/src/gtest-filepath.cc
gtest/internal/gtest-filepath.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-filepath.h
stdlib.h
-
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-port.h
gtest/gtest-message.h
/usr/src/googletest/googletest/src/gtest/gtest-message.h
windows.h
-
direct.h
-
io.h
-
limits.h
-
climits
-
gtest/internal/gtest-string.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-string.h

/usr/src/googletest/googletest/src/gtest-internal-inl.h
errno.h
-
stddef.h
-
stdlib.h
-
string.h
-
algorithm
-
memory
-
string
-
vector
-
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-port.h
arpa/inet.h
-
netdb.h
-
windows.h
-
gtest/gtest.h
/usr/src/googletest/googletest/src/gtest/gtest.h
gtest/gtest-spi.h
/usr/src/googletest/googletest/src/gtest/gtest-spi.h

/usr/src/googletest/googletest/src/gtest-matchers.cc
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-port.h
gtest/gtest-matchers.h
/usr/src/googletest/googletest/src/gtest/gtest-matchers.h
string
-

/usr/src/googletest/googletest/src/gtest-port.cc
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-port.h
limits.h
-
stdio.h
-
stdlib.h
-
string.h
-
fstream
-
memory
-
windows.h
-
io.h
-
sys/stat.h
-
map
-
crtdbg.h
-
unistd.h
-
mach/mach_init.h
-
mach/task.h
-
mach/vm_map.h
-
sys/sysctl.h
-
sys/user.h
-
devctl.h
-
fcntl.h
-
sys/procfs.h
-
procinfo.h
-
sys/types.h
-
zircon/process.h
-
zircon/syscalls.h
-
gtest/gtest-spi.h
/usr/src/googletest/googletest/src/gtest/gtest-spi.h
gtest/gtest-message.h
/usr/src/googletest/googletest/src/gtest/gtest-message.h
gtest/internal/gtest-internal.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-internal.h
gtest/internal/gtest-string.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-string.h
src/gtest-internal-inl.h
/usr/src/googletest/googletest/src/src/gtest-internal-inl.h

/usr/src/googletest/googletest/src/gtest-printers.cc
gtest/gtest-printers.h
/usr/src/googletest/googletest/src/gtest/gtest-printers.h
stdio.h
-
cctype
-
cwchar
-
ostream
-
string
-
gtest/internal/gtest-port.h
/usr/src/googletest/googletest/src/gtest/internal/gtest-port.h
src/gtest-internal-inl.h
/usr/src/googletest/googletest/src/src/gtest-internal-inl.h

/usr/src/googletest/googletest/src/gtest-test-part.cc
gtest/gtest-test-part.h
/usr/src/googletest/googletest/src/gtest/gtest-test-part.h
src/gtest-internal-inl.h
/usr/src/googletest/googletest/src/src/gtest-internal-inl.h

/usr/src/googletest/googletest/src/gtest-typed-test.cc
gtest/gtest-typed-test.h
/usr/src/googletest/googletest/src/gtest/gtest-typed-test.h
gtest/gtest.h
/usr/src/googletest/googletest/src/gtest/gtest.h

/usr/src/googletest/googletest/src/gtest.cc
gtest/gtest.h
/usr/src/googletest/googletest/src/gtest/gtest.h
gtest/internal/custom/gtest.h
/usr/src/googletest/googletest/src/gtest/internal/custom/gtest.h
gtest/gtest-spi.h
/usr/src/googletest/googletest/src/gtest/gtest-spi.h
ctype.h
-
cmath
-
stdarg.h
-
stdio.h
-
stdlib.h
-
time.h
-
wchar.h
-
wctype.h
-
algorithm
-
iomanip
-
limits
-
list
-
map
-
ostream
-
sstream
-
vector
-
fcntl.h
-
limits.h
-
sched.h
-
strings.h
-
sys/mman.h
-
sys/time.h
-
unistd.h
-
string
-
sys/time.h
-
strings.h
-
windows.h
-
windows.h
-
crtdbg.h
-
debugapi.h
-
io.h
-
sys/timeb.h
-
sys/types.h
-
sys/stat.h
-
sys/time.h
-
sys/time.h
-
unistd.h
-
stdexcept
-
arpa/inet.h
-
netdb.h
-
sys/socket.h
-
sys/types.h
-
src/gtest-internal-inl.h
/usr/src/googletest/googletest/src/src/gtest-internal-inl.h
crt_externs.h
-
absl/debugging/failure_signal_handler.h
/usr/src/googletest/googletest/src/absl/debugging/failure_signal_handler.h
absl/debugging/stacktrace.h
/usr/src/googletest/googletest/src/absl/debugging/stacktrace.h
absl/debugging/symbolize.h
/usr/src/googletest/googletest/src/absl/debugging/symbolize.h
absl/strings/str_cat.h
/usr/src/googletest/googletest/src/absl/strings/str_cat.h

