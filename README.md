# 差速履带扫雪车规划控制模块

## 项目简介

本项目是一个基于ROS的差速履带扫雪车自主导航系统，实现了从全局路径规划到局部轨迹跟踪的完整导航解决方案。系统采用分层架构设计，集成了多种先进的路径规划和控制算法。

## 系统架构

```
感知层 → 规划层 → 控制层 → 执行层
  ↓       ↓       ↓       ↓
传感器   路径规划  运动控制  差速履带
```

## 核心功能模块

- **全局路径规划**: A*算法、Dijkstra算法
- **局部路径规划**: 直线规划器、DWA规划器
- **轨迹优化**: Frenet坐标系轨迹规划
- **运动控制**: Pure Pursuit路径跟踪控制
- **安全监控**: 急停、心跳监控、障碍物检测

## 快速开始

### 1. 环境配置

```bash
# 安装ROS (Ubuntu 18.04/20.04)
sudo apt update
sudo apt install ros-melodic-desktop-full  # 或 ros-noetic-desktop-full

# 安装依赖
sudo apt install ros-$ROS_DISTRO-navigation
sudo apt install ros-$ROS_DISTRO-map-server
sudo apt install libeigen3-dev

# 设置ROS环境
echo "source /opt/ros/$ROS_DISTRO/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

### 2. 编译项目

```bash
# 进入工作空间
cd ~/demo_ws

# 安装依赖包
rosdep install --from-paths src --ignore-src -r -y

# 编译
catkin_make
# 或使用 catkin build (推荐)

# 设置环境变量
source devel/setup.bash
echo "source ~/demo_ws/devel/setup.bash" >> ~/.bashrc
```

### 3. 快速启动

```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 启动仿真模式 (推荐新手)
./scripts/start_navigation_system.sh simulation

# 启动真实机器人模式
./scripts/start_navigation_system.sh real

# 启动测试模式
./scripts/start_navigation_system.sh test
```

### 4. 功能测试

```bash
# 测试所有模块
./scripts/test_modules.sh all

# 测试单个模块
./scripts/test_modules.sh astar      # A*路径规划器
./scripts/test_modules.sh straight   # 直线路径规划器
./scripts/test_modules.sh control    # Pure Pursuit控制器
./scripts/test_modules.sh frenet     # Frenet轨迹规划器
```

## 手动启动指南

### 启动核心节点

```bash
# 1. 启动roscore
roscore

# 2. 启动地图服务器
rosrun map_server map_server your_map.yaml

# 3. 启动A*全局路径规划器
rosrun a_star_path_planning a_star_path_planner

# 4. 启动直线局部路径规划器
rosrun straight_path_planner straight_path_planner

# 5. 启动Pure Pursuit控制器
rosrun circle_control circle_control

# 6. 启动可视化
rviz
```

### 发送导航指令

```bash
# 发布目标点
rostopic pub /move_base_simple/goal geometry_msgs/PoseStamped "
header:
  frame_id: 'map'
pose:
  position:
    x: 5.0
    y: 3.0
    z: 0.0
  orientation:
    w: 1.0"

# 启动任务
rostopic pub /TaskCtl std_msgs/Int64 "data: 1"

# 紧急停止
rostopic pub /EStop std_msgs/Int64 "data: 1"
```

## 主要话题接口

| 话题名称 | 消息类型 | 功能描述 |
|---------|---------|---------|
| `/grid_map` | nav_msgs/OccupancyGrid | 全局地图数据 |
| `/odom` | nav_msgs/Odometry | 机器人里程计 |
| `/path` | nav_msgs/Path | 全局规划路径 |
| `/local_path` | nav_msgs/Path | 局部规划路径 |
| `/cmd_vel` | geometry_msgs/Twist | 速度控制指令 |
| `/move_base_simple/goal` | geometry_msgs/PoseStamped | 目标点 |
| `/TaskCtl` | std_msgs/Int64 | 任务控制指令 |
| `/EStop` | std_msgs/Int64 | 急停指令 |

## 参数配置

### 路径规划参数

```yaml
# A*规划器参数
robot_radius: 0.2
inflation_radius: 0.3

# 直线规划器参数
step_length: 0.1
max_length: 2.0
cost_threshold: 50
```

### 控制器参数

```yaml
# Pure Pursuit参数
lookahead_distance: 1.75
max_linear_velocity: 0.8
max_angular_velocity: 1.0

# 安全参数
emergency_stop_distance: 1.0
safety_margin: 0.5
```

## 故障排除

### 常见问题

1. **编译错误**
   ```bash
   # 检查依赖
   rosdep check --from-paths src --ignore-src
   
   # 重新安装依赖
   rosdep install --from-paths src --ignore-src -r -y
   ```

2. **节点无法启动**
   ```bash
   # 检查ROS环境
   echo $ROS_DISTRO
   
   # 重新source环境
   source devel/setup.bash
   ```

3. **话题无数据**
   ```bash
   # 检查话题列表
   rostopic list
   
   # 检查话题信息
   rostopic info /topic_name
   
   # 监控话题数据
   rostopic echo /topic_name
   ```

### 调试工具

```bash
# 查看节点图
rosrun rqt_graph rqt_graph

# 监控系统性能
rosrun rqt_top rqt_top

# 查看日志
rosrun rqt_console rqt_console

# 录制数据
rosbag record -a

# 回放数据
rosbag play your_bag.bag
```

## 开发指南

### 添加新算法

1. 在相应模块目录下创建新的算法类
2. 继承基础接口类
3. 实现核心算法函数
4. 添加ROS接口封装
5. 更新CMakeLists.txt和package.xml

### 代码结构

```
src/
├── a_star_path_planning/     # A*路径规划
├── straight_path_planner/    # 直线路径规划
├── path_and_pose_subscriber/ # Frenet轨迹规划
├── circle_control/           # Pure Pursuit控制
├── navigation/               # ROS导航包
└── tutorials/                # 教程和配置
```

## 性能优化

- 使用多线程提高实时性
- 优化算法参数
- 减少不必要的计算
- 合理设置话题发布频率

## 安全注意事项

- 始终保持急停功能可用
- 定期检查传感器状态
- 监控系统心跳信号
- 设置合理的安全距离

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: 扫雪车项目组
- 邮箱: [项目邮箱]
- 文档: [技术文档链接]

## 更新日志

### v1.0.0 (2025-07-10)
- 初始版本发布
- 实现基础导航功能
- 添加多种路径规划算法
- 完善安全监控机制

---

**注意**: 在实际部署前，请仔细阅读技术文档并进行充分测试。
