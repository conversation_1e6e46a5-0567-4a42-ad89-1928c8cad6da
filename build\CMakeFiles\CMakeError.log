Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_8f1bc/fast && /usr/bin/make -f CMakeFiles/cmTC_8f1bc.dir/build.make CMakeFiles/cmTC_8f1bc.dir/build
make[1]: 进入目录“/home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_8f1bc.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_8f1bc.dir/src.c.o   -c /home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_8f1bc
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8f1bc.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    -rdynamic CMakeFiles/cmTC_8f1bc.dir/src.c.o  -o cmTC_8f1bc 
/usr/bin/ld: CMakeFiles/cmTC_8f1bc.dir/src.c.o: in function `main':
src.c:(.text+0x48): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x50): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x5c): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_8f1bc.dir/build.make:87：cmTC_8f1bc] 错误 1
make[1]: 离开目录“/home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_8f1bc/fast] 错误 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_0b562/fast && /usr/bin/make -f CMakeFiles/cmTC_0b562.dir/build.make CMakeFiles/cmTC_0b562.dir/build
make[1]: 进入目录“/home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_0b562.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_0b562.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_0b562
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0b562.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_0b562.dir/CheckFunctionExists.c.o  -o cmTC_0b562  -lpthreads 
/usr/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_0b562.dir/build.make:87：cmTC_0b562] 错误 1
make[1]: 离开目录“/home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_0b562/fast] 错误 2



Determining if the include file pgm.h exists failed with the following output:
Change Dir: /home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_33956/fast && /usr/bin/make -f CMakeFiles/cmTC_33956.dir/build.make CMakeFiles/cmTC_33956.dir/build
make[1]: 进入目录“/home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp”
Building CXX object CMakeFiles/cmTC_33956.dir/CheckIncludeFile.cxx.o
/usr/bin/c++     -o CMakeFiles/cmTC_33956.dir/CheckIncludeFile.cxx.o -c /home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp/CheckIncludeFile.cxx
/home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp/CheckIncludeFile.cxx:1:10: fatal error: pgm.h: 没有那个文件或目录
    1 | #include <pgm.h>
      |          ^~~~~~~
compilation terminated.
make[1]: *** [CMakeFiles/cmTC_33956.dir/build.make:66：CMakeFiles/cmTC_33956.dir/CheckIncludeFile.cxx.o] 错误 1
make[1]: 离开目录“/home/<USER>/demo_ws/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_33956/fast] 错误 2



