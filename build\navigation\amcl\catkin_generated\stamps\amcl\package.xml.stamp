<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
    <name>amcl</name>
    <version>1.17.3</version>
    <description>
        <p>
            amcl is a probabilistic localization system for a robot moving in
            2D. It implements the adaptive (or KLD-sampling) Monte Carlo
            localization approach (as described by <PERSON><PERSON>), which uses a
            particle filter to track the pose of a robot against a known map.
        </p>
        <p>
            This node is derived, with thanks, from <PERSON>'s excellent
            'amcl' Player driver.
        </p>
    </description>
    <url>http://wiki.ros.org/amcl</url>
    <author><PERSON></author>
    <author><EMAIL></author>
    <maintainer email="<EMAIL>">David V. Lu!!</maintainer>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <license>LGPL</license>

    <buildtool_depend>catkin</buildtool_depend>

    <build_depend>message_filters</build_depend>
    <build_depend>tf2_geometry_msgs</build_depend>

    <depend>diagnostic_updater</depend>
    <depend>dynamic_reconfigure</depend>
    <depend>geometry_msgs</depend>
    <depend>nav_msgs</depend>
    <depend>rosbag</depend>
    <depend>roscpp</depend>
    <depend>sensor_msgs</depend>
    <depend>std_srvs</depend>
    <depend>tf2</depend>
    <depend>tf2_msgs</depend>
    <depend>tf2_ros</depend>

    <test_depend>map_server</test_depend>
    <test_depend>rostest</test_depend>
    <test_depend>python3-pykdl</test_depend>
    <test_depend>tf2_py</test_depend>
</package>
