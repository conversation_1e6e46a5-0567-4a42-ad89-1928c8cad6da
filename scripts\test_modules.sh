#!/bin/bash

# 差速履带扫雪车各模块功能测试脚本
# 使用方法: ./test_modules.sh [模块名]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查节点是否运行
check_node_running() {
    local node_name=$1
    if rosnode list | grep -q "$node_name"; then
        return 0
    else
        return 1
    fi
}

# 检查话题是否有数据
check_topic_data() {
    local topic_name=$1
    local timeout=${2:-5}
    
    print_info "检查话题 $topic_name 是否有数据..."
    
    if timeout $timeout rostopic echo $topic_name -n 1 >/dev/null 2>&1; then
        print_success "话题 $topic_name 有数据"
        return 0
    else
        print_error "话题 $topic_name 无数据或超时"
        return 1
    fi
}

# 测试A*路径规划器
test_astar_planner() {
    print_info "测试A*路径规划器..."
    
    # 启动节点
    print_info "启动A*路径规划器节点..."
    rosrun a_star_path_planning a_star_path_planner &
    local astar_pid=$!
    sleep 3
    
    # 检查节点是否运行
    if check_node_running "a_star_path_planner"; then
        print_success "A*路径规划器节点启动成功"
    else
        print_error "A*路径规划器节点启动失败"
        kill $astar_pid 2>/dev/null || true
        return 1
    fi
    
    # 发布测试地图
    print_info "发布测试地图数据..."
    rostopic pub /grid_map nav_msgs/OccupancyGrid "
header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: 'map'
info:
  map_load_time:
    secs: 0
    nsecs: 0
  resolution: 0.1
  width: 100
  height: 100
  origin:
    position:
      x: -5.0
      y: -5.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
data: [0, 0, 0, 0, 0]" &
    
    sleep 2
    
    # 发布起点
    print_info "发布起点..."
    rostopic pub /amcl_pose geometry_msgs/PoseWithCovarianceStamped "
header:
  frame_id: 'map'
pose:
  pose:
    position:
      x: 0.0
      y: 0.0
      z: 0.0
    orientation:
      w: 1.0" &
    
    sleep 1
    
    # 发布目标点
    print_info "发布目标点..."
    rostopic pub /move_base_simple/goal geometry_msgs/PoseStamped "
header:
  frame_id: 'map'
pose:
  position:
    x: 5.0
    y: 3.0
    z: 0.0
  orientation:
    w: 1.0" &
    
    sleep 2
    
    # 检查输出路径
    if check_topic_data "/path" 10; then
        print_success "A*路径规划器测试通过"
    else
        print_error "A*路径规划器测试失败"
    fi
    
    # 清理
    kill $astar_pid 2>/dev/null || true
    killall rostopic 2>/dev/null || true
}

# 测试直线路径规划器
test_straight_planner() {
    print_info "测试直线路径规划器..."
    
    # 启动节点
    rosrun straight_path_planner straight_path_planner &
    local straight_pid=$!
    sleep 3
    
    # 检查节点运行状态
    if check_node_running "straight_path_planner"; then
        print_success "直线路径规划器节点启动成功"
    else
        print_error "直线路径规划器节点启动失败"
        kill $straight_pid 2>/dev/null || true
        return 1
    fi
    
    # 发布代价地图
    print_info "发布代价地图数据..."
    rostopic pub /move_base/local_costmap/costmap nav_msgs/OccupancyGrid "
header:
  frame_id: 'map'
info:
  resolution: 0.05
  width: 200
  height: 200
  origin:
    position:
      x: -5.0
      y: -5.0
      z: 0.0
data: [0, 0, 0, 0, 0]" &
    
    sleep 3
    
    # 检查输出
    if check_topic_data "/local_path" 10; then
        print_success "直线路径规划器测试通过"
    else
        print_error "直线路径规划器测试失败"
    fi
    
    # 清理
    kill $straight_pid 2>/dev/null || true
    killall rostopic 2>/dev/null || true
}

# 测试Pure Pursuit控制器
test_pure_pursuit() {
    print_info "测试Pure Pursuit控制器..."
    
    # 启动节点
    rosrun circle_control circle_control &
    local control_pid=$!
    sleep 3
    
    # 检查节点运行状态
    if check_node_running "circle_control"; then
        print_success "Pure Pursuit控制器节点启动成功"
    else
        print_error "Pure Pursuit控制器节点启动失败"
        kill $control_pid 2>/dev/null || true
        return 1
    fi
    
    # 发布里程计数据
    print_info "发布里程计数据..."
    rostopic pub /odom nav_msgs/Odometry "
header:
  frame_id: 'odom'
child_frame_id: 'base_link'
pose:
  pose:
    position:
      x: 0.0
      y: 0.0
      z: 0.0
    orientation:
      w: 1.0" &
    
    sleep 1
    
    # 发布测试路径
    print_info "发布测试路径..."
    rostopic pub /local_path nav_msgs/Path "
header:
  frame_id: 'map'
poses:
- header:
    frame_id: 'map'
  pose:
    position:
      x: 1.0
      y: 0.0
      z: 0.0
    orientation:
      w: 1.0
- header:
    frame_id: 'map'
  pose:
    position:
      x: 2.0
      y: 0.0
      z: 0.0
    orientation:
      w: 1.0" &
    
    sleep 1
    
    # 启动任务
    print_info "启动控制任务..."
    rostopic pub /TaskCtl std_msgs/Int64 "data: 1" &
    
    sleep 2
    
    # 检查控制输出
    if check_topic_data "/cmd_vel" 5; then
        print_success "Pure Pursuit控制器测试通过"
    else
        print_error "Pure Pursuit控制器测试失败"
    fi
    
    # 测试急停功能
    print_info "测试急停功能..."
    rostopic pub /EStop std_msgs/Int64 "data: 1" &
    sleep 1
    
    # 清理
    kill $control_pid 2>/dev/null || true
    killall rostopic 2>/dev/null || true
}

# 测试Frenet轨迹规划器
test_frenet_planner() {
    print_info "测试Frenet轨迹规划器..."
    
    # 运行测试程序
    print_info "运行Frenet测试程序..."
    if rosrun path_and_pose_subscriber main_test; then
        print_success "Frenet轨迹规划器测试通过"
    else
        print_error "Frenet轨迹规划器测试失败"
        return 1
    fi
}

# 系统集成测试
test_integration() {
    print_info "进行系统集成测试..."
    
    # 启动所有核心节点
    print_info "启动核心节点..."
    
    # 启动A*规划器
    rosrun a_star_path_planning a_star_path_planner &
    local astar_pid=$!
    
    # 启动直线规划器
    rosrun straight_path_planner straight_path_planner &
    local straight_pid=$!
    
    # 启动控制器
    rosrun circle_control circle_control &
    local control_pid=$!
    
    sleep 5
    
    # 检查所有节点是否运行
    local all_running=true
    
    if ! check_node_running "a_star_path_planner"; then
        print_error "A*规划器未运行"
        all_running=false
    fi
    
    if ! check_node_running "straight_path_planner"; then
        print_error "直线规划器未运行"
        all_running=false
    fi
    
    if ! check_node_running "circle_control"; then
        print_error "控制器未运行"
        all_running=false
    fi
    
    if $all_running; then
        print_success "所有核心节点运行正常"
        
        # 进行数据流测试
        print_info "测试数据流..."
        
        # 发布测试数据并检查数据流
        # ... (这里可以添加更详细的集成测试)
        
        print_success "系统集成测试通过"
    else
        print_error "系统集成测试失败"
    fi
    
    # 清理所有进程
    kill $astar_pid $straight_pid $control_pid 2>/dev/null || true
}

# 显示帮助信息
show_help() {
    echo "差速履带扫雪车模块测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [模块名]"
    echo ""
    echo "模块选项:"
    echo "  astar       - 测试A*路径规划器"
    echo "  straight    - 测试直线路径规划器"
    echo "  control     - 测试Pure Pursuit控制器"
    echo "  frenet      - 测试Frenet轨迹规划器"
    echo "  integration - 系统集成测试"
    echo "  all         - 测试所有模块 (默认)"
    echo "  help        - 显示此帮助信息"
}

# 主函数
main() {
    print_info "差速履带扫雪车模块测试"
    print_info "========================"
    
    # 检查ROS环境
    if [ -z "$ROS_DISTRO" ]; then
        print_error "ROS环境未设置"
        exit 1
    fi
    
    # 检查roscore是否运行
    if ! pgrep -f roscore >/dev/null; then
        print_info "启动roscore..."
        roscore &
        sleep 3
    fi
    
    # 设置环境
    source devel/setup.bash 2>/dev/null || true
    
    case "${1:-all}" in
        "astar")
            test_astar_planner
            ;;
        "straight")
            test_straight_planner
            ;;
        "control")
            test_pure_pursuit
            ;;
        "frenet")
            test_frenet_planner
            ;;
        "integration")
            test_integration
            ;;
        "all")
            test_astar_planner
            test_straight_planner
            test_pure_pursuit
            test_frenet_planner
            test_integration
            ;;
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        *)
            print_error "未知模块: $1"
            show_help
            exit 1
            ;;
    esac
    
    print_success "测试完成！"
}

# 运行主函数
main "$@"
