#!/bin/bash

# 差速履带扫雪车导航系统启动脚本
# 使用方法: ./start_navigation_system.sh [模式]
# 模式选项: simulation, real, test

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查ROS环境
check_ros_environment() {
    print_info "检查ROS环境..."
    
    if [ -z "$ROS_DISTRO" ]; then
        print_error "ROS环境未设置，请先source ROS setup文件"
        exit 1
    fi
    
    if [ ! -f "devel/setup.bash" ]; then
        print_error "工作空间未编译，请先运行 catkin_make"
        exit 1
    fi
    
    source devel/setup.bash
    print_success "ROS环境检查完成 (ROS $ROS_DISTRO)"
}

# 启动仿真模式
start_simulation() {
    print_info "启动仿真模式..."
    
    # 启动Gazebo仿真环境
    print_info "启动Gazebo仿真环境..."
    gnome-terminal --tab --title="Gazebo" -- bash -c "
        source devel/setup.bash
        roslaunch tutorials gazebo_world.launch
        exec bash"
    
    sleep 5
    
    # 启动机器人模型
    print_info "启动机器人模型..."
    gnome-terminal --tab --title="Robot" -- bash -c "
        source devel/setup.bash
        roslaunch tutorials robot.launch
        exec bash"
    
    sleep 3
    
    # 启动导航系统
    start_navigation_core
    
    # 启动rviz可视化
    print_info "启动rviz可视化..."
    gnome-terminal --tab --title="RViz" -- bash -c "
        source devel/setup.bash
        sleep 5
        rviz -d src/tutorials/rviz/navigation.rviz 2>/dev/null || rviz
        exec bash"
}

# 启动真实机器人模式
start_real_robot() {
    print_info "启动真实机器人模式..."
    
    # 启动底盘驱动
    print_info "启动底盘驱动..."
    gnome-terminal --tab --title="Base Driver" -- bash -c "
        source devel/setup.bash
        # 这里需要根据实际硬件接口修改
        # roslaunch your_robot_driver robot_driver.launch
        echo '请手动启动底盘驱动节点'
        exec bash"
    
    # 启动传感器
    print_info "启动传感器..."
    gnome-terminal --tab --title="Sensors" -- bash -c "
        source devel/setup.bash
        # 启动激光雷达
        # roslaunch your_lidar_driver lidar.launch
        echo '请手动启动传感器节点'
        exec bash"
    
    sleep 3
    
    # 启动定位
    print_info "启动定位系统..."
    gnome-terminal --tab --title="Localization" -- bash -c "
        source devel/setup.bash
        roslaunch navigation amcl.launch
        exec bash"
    
    sleep 2
    
    # 启动导航系统
    start_navigation_core
}

# 启动导航核心模块
start_navigation_core() {
    print_info "启动导航核心模块..."
    
    # 启动地图服务器
    print_info "启动地图服务器..."
    gnome-terminal --tab --title="Map Server" -- bash -c "
        source devel/setup.bash
        # 需要根据实际地图文件路径修改
        rosrun map_server map_server src/tutorials/maps/demo_map.yaml 2>/dev/null || echo '请提供正确的地图文件'
        exec bash"
    
    sleep 2
    
    # 启动全局路径规划器
    print_info "启动A*全局路径规划器..."
    gnome-terminal --tab --title="Global Planner" -- bash -c "
        source devel/setup.bash
        rosrun a_star_path_planning a_star_path_planner
        exec bash"
    
    sleep 1
    
    # 启动局部路径规划器
    print_info "启动直线局部路径规划器..."
    gnome-terminal --tab --title="Local Planner" -- bash -c "
        source devel/setup.bash
        rosrun straight_path_planner straight_path_planner
        exec bash"
    
    sleep 1
    
    # 启动Pure Pursuit控制器
    print_info "启动Pure Pursuit控制器..."
    gnome-terminal --tab --title="Controller" -- bash -c "
        source devel/setup.bash
        rosrun circle_control circle_control
        exec bash"
    
    sleep 1
    
    print_success "导航系统启动完成！"
}

# 启动测试模式
start_test_mode() {
    print_info "启动测试模式..."
    
    # 启动roscore
    print_info "启动ROS核心..."
    gnome-terminal --tab --title="ROS Core" -- bash -c "
        roscore
        exec bash"
    
    sleep 3
    
    # 测试A*路径规划器
    print_info "测试A*路径规划器..."
    gnome-terminal --tab --title="A* Test" -- bash -c "
        source devel/setup.bash
        echo '启动A*路径规划器测试...'
        rosrun a_star_path_planning a_star_path_planner &
        sleep 2
        echo '发布测试地图和目标点...'
        # 这里可以添加测试数据发布
        exec bash"
    
    # 测试Frenet轨迹规划器
    print_info "测试Frenet轨迹规划器..."
    gnome-terminal --tab --title="Frenet Test" -- bash -c "
        source devel/setup.bash
        echo '启动Frenet轨迹规划器测试...'
        rosrun path_and_pose_subscriber main_test
        exec bash"
    
    # 测试控制器
    print_info "测试Pure Pursuit控制器..."
    gnome-terminal --tab --title="Controller Test" -- bash -c "
        source devel/setup.bash
        echo '启动控制器测试...'
        rosrun circle_control circle_control &
        sleep 2
        echo '发布测试路径...'
        # 这里可以添加测试路径发布
        exec bash"
}

# 显示使用帮助
show_help() {
    echo "差速履带扫雪车导航系统启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [模式]"
    echo ""
    echo "模式选项:"
    echo "  simulation  - 启动仿真模式 (默认)"
    echo "  real        - 启动真实机器人模式"
    echo "  test        - 启动测试模式"
    echo "  help        - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 simulation   # 启动仿真模式"
    echo "  $0 real         # 启动真实机器人模式"
    echo "  $0 test         # 启动测试模式"
}

# 清理函数
cleanup() {
    print_info "正在清理进程..."
    # 这里可以添加清理逻辑
    print_success "清理完成"
}

# 设置信号处理
trap cleanup EXIT

# 主函数
main() {
    print_info "差速履带扫雪车导航系统启动脚本"
    print_info "========================================"
    
    # 检查ROS环境
    check_ros_environment
    
    # 根据参数选择启动模式
    case "${1:-simulation}" in
        "simulation")
            start_simulation
            ;;
        "real")
            start_real_robot
            ;;
        "test")
            start_test_mode
            ;;
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        *)
            print_error "未知模式: $1"
            show_help
            exit 1
            ;;
    esac
    
    print_success "系统启动完成！"
    print_info "使用 Ctrl+C 退出"
    
    # 等待用户中断
    while true; do
        sleep 1
    done
}

# 运行主函数
main "$@"
